// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class DrawTubeManager extends cc.Component {
  @property(cc.Node)
  drawTube: cc.Node = null;

  @property(cc.Node)
  drawTubeGif: cc.Node = null;

  @property(cc.Node)
  maskLottery: cc.Node = null;

  @property(cc.Node)
  fail: cc.Node = null;

  @property(cc.Node)
  win: cc.Node = null;

  @property(cc.Label)
  prizeText: cc.Label = null;

  @property(cc.Node)
  acceptBtn: cc.Node = null;

  duration: number = 0.8;

  // 添加自定义缩放变量，可以被外部组件访问和修改
  public customScale: number = 1;

  // {"code":200,"msg":"成功","data":{"recordId":16,"msg":"5元优惠券","prizeImage":null,"prizeId":2,"prizeType":2},"success":true}
  // {"code":200,"msg":"成功","data":{"recordId":18,"msg":"5金币","prizeImage":null,"prizeId":3,"prizeType":7},"success":true}
  onLoad() {
    // this.node.scale = 0.7;
    // this.node.opacity = 100;
    // if (this.lotteryInfo?.json?.prizeType) {
    //   this.showAward();
    //   this.goldNumText.string = this.lotteryInfo.json.msg.toString();
    //   this.showGold(); // 暂不区分金币和优惠券，统一用金币的样式展示
    // } else {
    //   this.showFail();
    // }
  }

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  againBtnClick() {
    this.backToLevelSelect();
  }

  acceptBtnClick() {
    this.drawTube.active = true;
    this.maskLottery.active = false;
    this.win.active = false;
    this.fail.active = false;
  }

  showFail() {
    // 显示失败，隐藏中奖
    this.drawTubeGif.active = false;
    this.fail.active = true;
  }

  showAward() {
    this.maskLottery.active = true;
    this.drawTubeGif.active = true;
    this.drawTubeGif.anchorX = 0;
    this.drawTubeGif.anchorY = 0.5;

    // 优化后的左右摇摆动画，更加自然流畅
    this.playDrawTubeShakeAnimation().then(() => {
      // 动画完成后显示中奖结果
      // this.drawTube.active = false;
      // this.showWin();
    });
  }

  playDrawTubeShakeAnimation(): Promise<void> {
    return new Promise(resolve => {
      // 设置签筒锚点为底部中间 (0, 0.5)
      this.drawTubeGif.anchorX = 0;
      this.drawTubeGif.anchorY = 0.5;

      // 记录初始角度（已旋转180度）
      const initialAngle = this.drawTubeGif.angle;

      // 使用渐进式摇摆动画：慢启动-快摇摆-慢结束
      this.playProgressiveShakeAnimation(initialAngle);

      // 显示动画节点，隐藏静态节点
      this.drawTubeGif.active = true;
      // 假设还有静态图片节点需要隐藏

      // 5秒后停止动画
      setTimeout(() => {
        cc.Tween.stopAllByTarget(this.drawTubeGif);
        this.drawTubeGif.angle = initialAngle; // 恢复初始角度
        resolve();
      }, 5000);
    });
  }

  /**
   * 渐进式摇摆动画：摇摆两圈后停止
   * @param initialAngle 初始角度
   */
  playProgressiveShakeAnimation(initialAngle: number): void {
    let shakeCount = 0;
    const maxShakes = 2; // 摇摆2圈（左右摇摆算一圈）
    let currentTween: cc.Tween = null;

    const executeShakeSequence = () => {
      if (shakeCount >= maxShakes) {
        // 摇摆完成，停止动画并恢复初始角度
        cc.tween(this.drawTubeGif)
          .to(0.3, { angle: initialAngle }, { easing: 'quadOut' })
          .start();
        return;
      }

      shakeCount++;

      // 根据摇摆次数计算速度和幅度
      let speedMultiplier = 1;
      let amplitudeMultiplier = 1;

      if (shakeCount === 1) {
        // 第1圈：激烈摇摆
        speedMultiplier = 0.3;
        amplitudeMultiplier = 2.5;
      } else if (shakeCount === 2) {
        // 第2圈：更加激烈
        speedMultiplier = 0.25;
        amplitudeMultiplier = 3.0;
      }

      // 更大的摇摆角度，增加激烈感
      const baseAngle = 25 + Math.random() * 10; // 25-35度基础角度
      const leftAngle = initialAngle - baseAngle * amplitudeMultiplier;
      const rightAngle = initialAngle + baseAngle * amplitudeMultiplier;

      // 快速摇摆时间
      const leftTime = 0.15 * speedMultiplier;
      const rightTime = 0.2 * speedMultiplier;
      const centerTime = 0.1 * speedMultiplier;

      // 执行一圈摇摆：左->右->中
      cc.tween(this.drawTubeGif)
        .to(leftTime, { angle: leftAngle }, { easing: 'quadOut' })
        .to(rightTime, { angle: rightAngle }, { easing: 'sineInOut' })
        .to(centerTime, { angle: initialAngle }, { easing: 'quadIn' })
        .call(() => {
          // 一圈完成后，继续下一圈或停止
          executeShakeSequence();
        })
        .start();
    };

    // 开始执行摇摆序列
    executeShakeSequence();
  }

  showWin() {
    // 隐藏其他元素
    this.drawTubeGif.active = false;
    this.fail.active = false;

    // 显示win节点并播放入场动画
    this.win.active = true;

    // 设置初始状态：缩放为0，透明度为0
    this.win.scale = 0;
    this.win.opacity = 0;

    // 播放弹性入场动画
    cc.tween(this.win)
      .to(0.3, { scale: 1.1, opacity: 255 }, { easing: 'backOut' })
      .to(0.15, { scale: 1.0 }, { easing: 'sineInOut' })
      .start();
  }
  updatePrizeText(text: string): void {
    // 更新数字内容
    this.prizeText.string = text;
    // setTimeout(() => {
    //   const amountWidth = this.goldNumAmount.node.width;
    //   const currentX = this.goldNumText.node.x; // 获取当前X位置
    //   this.goldNumAmount.node.x = currentX - amountWidth / 2 - 10;
    // }, 0); // 0毫秒的延迟
  }
  protected onEnable(): void {
    this.node.scale = 0.5;
    this.node.opacity = 100;
    cc.tween(this.node)
      .then(
        cc.tween().to(this.duration, { scale: this.customScale, opacity: 255 })
      )
      .start();

    cc.tween(this.acceptBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();
  }

  // update (dt) {}
}
