// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class DrawTubeManager extends cc.Component {
  @property(cc.Node)
  drawTube: cc.Node = null;

  @property(cc.Node)
  drawTubeGif: cc.Node = null;

  @property(cc.Node)
  maskLottery: cc.Node = null;

  @property(cc.Node)
  fail: cc.Node = null;

  @property(cc.Node)
  win: cc.Node = null;

  @property(cc.Label)
  prizeText: cc.Label = null;

  @property(cc.Node)
  acceptBtn: cc.Node = null;

  duration: number = 0.8;

  // 添加自定义缩放变量，可以被外部组件访问和修改
  public customScale: number = 1;

  // {"code":200,"msg":"成功","data":{"recordId":16,"msg":"5元优惠券","prizeImage":null,"prizeId":2,"prizeType":2},"success":true}
  // {"code":200,"msg":"成功","data":{"recordId":18,"msg":"5金币","prizeImage":null,"prizeId":3,"prizeType":7},"success":true}
  onLoad() {
    // this.node.scale = 0.7;
    // this.node.opacity = 100;
    // if (this.lotteryInfo?.json?.prizeType) {
    //   this.showAward();
    //   this.goldNumText.string = this.lotteryInfo.json.msg.toString();
    //   this.showGold(); // 暂不区分金币和优惠券，统一用金币的样式展示
    // } else {
    //   this.showFail();
    // }
  }

  backToLevelSelect() {
    globalVariables.showMarkBlack = false;
    cc.director.loadScene(AppConstants.LEVEL_SELECT_SCENE);
  }

  againBtnClick() {
    this.backToLevelSelect();
  }

  acceptBtnClick() {
    this.drawTube.active = true;
    this.maskLottery.active = false;
    this.win.active = false;
    this.fail.active = false;
  }

  showFail() {
    // 显示失败，隐藏中奖
    this.drawTubeGif.active = false;
    this.fail.active = true;
  }

  showAward() {
    this.maskLottery.active = true;
    this.drawTubeGif.active = true;
    this.drawTubeGif.anchorX = 0;
    this.drawTubeGif.anchorY = 0.5;

    // 优化后的左右摇摆动画，更加自然流畅
    this.playDrawTubeShakeAnimation().then(() => {
      // 动画完成后显示中奖结果
      // this.drawTube.active = false;
      // this.showWin();
    });
  }

  playDrawTubeShakeAnimation(): Promise<void> {
    return new Promise(resolve => {
      // 设置签筒锚点为底部中间 (0, 0.5)
      this.drawTubeGif.anchorX = 0;
      this.drawTubeGif.anchorY = 0.5;

      // 记录初始角度（已旋转180度）
      const initialAngle = this.drawTubeGif.angle;

      // 使用渐进式摇摆动画：慢启动-快摇摆-慢结束
      this.playProgressiveShakeAnimation(initialAngle);

      // 显示动画节点，隐藏静态节点
      this.drawTubeGif.active = true;
      // 假设还有静态图片节点需要隐藏

      // 5秒后停止动画
      setTimeout(() => {
        cc.Tween.stopAllByTarget(this.drawTubeGif);
        this.drawTubeGif.angle = initialAngle; // 恢复初始角度
        resolve();
      }, 5000);
    });
  }

  /**
   * 渐进式摇摆动画：慢启动-快摇摆-慢结束
   * @param initialAngle 初始角度
   */
  playProgressiveShakeAnimation(initialAngle: number): void {
    let shakeCount = 0;
    const totalDuration = 5000; // 总时长5秒
    const startTime = Date.now();

    const createProgressiveShakeSequence = () => {
      shakeCount++;
      const elapsed = Date.now() - startTime;
      const progress = elapsed / totalDuration; // 0-1的进度

      // 根据时间进度计算速度和幅度
      let speedMultiplier = 1;
      let amplitudeMultiplier = 1;

      if (progress < 0.2) {
        // 前20%时间：慢启动
        const startProgress = progress / 0.2; // 0-1
        speedMultiplier = 2.5 - startProgress * 1.5; // 2.5 -> 1.0
        amplitudeMultiplier = 0.4 + startProgress * 0.6; // 0.4 -> 1.0
      } else if (progress < 0.7) {
        // 中间50%时间：快速摇摆
        speedMultiplier = 0.5; // 最快速度
        amplitudeMultiplier = 1.3; // 最大幅度
      } else {
        // 后30%时间：逐渐减速
        const endProgress = (progress - 0.7) / 0.3; // 0-1
        speedMultiplier = 0.5 + endProgress * 2.5; // 0.5 -> 3.0
        amplitudeMultiplier = 1.3 - endProgress * 1.2; // 1.3 -> 0.1
      }

      // 随机化摇摆参数，增加自然感
      const baseAngle = 12 + Math.random() * 6; // 12-18度基础角度
      const leftAngle = initialAngle - baseAngle * amplitudeMultiplier;
      const rightAngle = initialAngle + baseAngle * amplitudeMultiplier;

      // 时间计算，应用速度倍数
      const baseLeftTime = 0.25 + Math.random() * 0.1;
      const baseRightTime = 0.3 + Math.random() * 0.15;
      const baseCenterTime = 0.2 + Math.random() * 0.1;

      const leftTime = baseLeftTime * speedMultiplier;
      const rightTime = baseRightTime * speedMultiplier;
      const centerTime = baseCenterTime * speedMultiplier;

      return cc
        .tween()
        .to(leftTime, { angle: leftAngle }, { easing: 'quadOut' })
        .to(rightTime, { angle: rightAngle }, { easing: 'sineInOut' })
        .to(centerTime, { angle: initialAngle }, { easing: 'quadIn' });
    };

    // 启动循环动画
    cc.tween(this.drawTubeGif)
      .repeatForever(createProgressiveShakeSequence())
      .start();
  }

  showWin() {
    // 隐藏其他元素
    this.drawTubeGif.active = false;
    this.fail.active = false;

    // 显示win节点并播放入场动画
    this.win.active = true;

    // 设置初始状态：缩放为0，透明度为0
    this.win.scale = 0;
    this.win.opacity = 0;

    // 播放弹性入场动画
    cc.tween(this.win)
      .to(0.3, { scale: 1.1, opacity: 255 }, { easing: 'backOut' })
      .to(0.15, { scale: 1.0 }, { easing: 'sineInOut' })
      .start();
  }
  updatePrizeText(text: string): void {
    // 更新数字内容
    this.prizeText.string = text;
    // setTimeout(() => {
    //   const amountWidth = this.goldNumAmount.node.width;
    //   const currentX = this.goldNumText.node.x; // 获取当前X位置
    //   this.goldNumAmount.node.x = currentX - amountWidth / 2 - 10;
    // }, 0); // 0毫秒的延迟
  }
  protected onEnable(): void {
    this.node.scale = 0.5;
    this.node.opacity = 100;
    cc.tween(this.node)
      .then(
        cc.tween().to(this.duration, { scale: this.customScale, opacity: 255 })
      )
      .start();

    cc.tween(this.acceptBtn)
      .then(ActionUtils.largenAndLessenAction(0.9, 0.8, 0.8))
      .repeatForever()
      .start();
  }

  // update (dt) {}
}
