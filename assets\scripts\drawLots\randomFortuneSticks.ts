// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  lose: cc.Node = null;

  duration: number = 0.8;

  onEnable() {
    const num = Math.floor(Math.random() * 6) + 1;
    cc.log(`🚀 ~ NewClass ~ showFail ~ num:', ${num}`);
    cc.resources.load(
      `drawLots/luckyDraw${num}`,
      cc.SpriteFrame,
      (err, spriteFrame) => {
        if (err) {
          cc.error('加载图片失败: ' + err);
          return;
        }
        // 使用 spriteFrame
        this.lose.getComponent(cc.Sprite).spriteFrame =
          spriteFrame as cc.SpriteFrame;
        this.lose.active = true;
      }
    );
  }

  start() {}

  // update (dt) {}
}
